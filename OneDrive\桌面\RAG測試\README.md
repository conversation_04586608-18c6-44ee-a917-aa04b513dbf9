# 大白菜食譜推薦系統

## 系統概述

這是一個基於向量化和餘弦相似度的食譜推薦系統，專門針對大白菜料理進行推薦。系統使用TF-IDF向量化技術將食譜內容轉換為數值向量，然後通過計算餘弦相似度來找出與使用者查詢最相關的食譜。

## 主要功能

1. **向量化處理**：使用TF-IDF將食譜文本轉換為向量
2. **中文分詞**：使用jieba進行中文文本分詞處理
3. **餘弦相似度計算**：計算查詢與食譜之間的相似度
4. **智能過濾**：自動識別素食需求並過濾含肉食譜
5. **前三名推薦**：返回最相關的前三個食譜

## 檔案結構

```
RAG測試/
├── recipe_recommendation_system.py  # 主要推薦系統類
├── demo_recipe_system.py           # 演示腳本
├── requirements.txt                 # 依賴套件
├── 大白菜_清理後食譜.csv            # 食譜資料庫
└── README.md                       # 說明文件
```

## 安裝與使用

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 運行演示

```bash
# 運行所有測試查詢
python recipe_recommendation_system.py

# 運行互動式演示
python demo_recipe_system.py
```

## 測試查詢與結果

### 1. 請推薦大白菜的食譜
- **第1名**：千層白菜豬肉鍋
- **第2名**：微辣黃金泡菜(五辛素)
- **第3名**：十分鐘上菜-白菜豆皮素食年菜

### 2. 我有雞蛋、大白菜和花生米可以做甚麼
- **第1名**：涼拌大白菜（相似度：0.1741）
- **第2名**：富貴花開一大白菜燒肉（相似度：0.0649）
- **第3名**：十分鐘上菜-白菜豆皮素食年菜（相似度：0.0482）

### 3. 清炒大白菜的作法
- **第1名**：三絲清炒大白菜(五辛素)（相似度：0.1304）
- **第2名**：大白菜炒韓式魚板（相似度：0.0731）
- **第3名**：微辣黃金泡菜(五辛素)

### 4. 適合寶寶吃的白菜粥
- **第1名**：自製酸白菜（相似度：0.2186）
- **第2名**：開陽白菜（相似度：0.1635）
- **第3名**：黃金泡菜/黃金白菜（相似度：0.1315）

### 5. 簡單快速的大白菜料理
- **第1名**：[簡單料理] 簡單卻美味的蒸千層大白菜（相似度：0.181）
- **第2名**：簡單肉羹（相似度：0.0864）
- **第3名**：酸白菜 製作~簡單隨手做（相似度：0.0653）

### 6. 沒有肉的大白菜料理
- **第1名**：開陽白菜（相似度：0.0517）
- **第2名**：十分鐘上菜-白菜豆皮素食年菜
- **第3名**：三絲清炒大白菜(五辛素)

## 技術特點

### 1. 文本預處理
- 移除特殊字符
- 中文分詞處理
- 過濾短詞和空白

### 2. 向量化技術
- TF-IDF向量化
- 支援1-2元語法
- 最大特徵數：5000

### 3. 智能過濾
- 自動識別素食查詢關鍵詞：['沒有肉', '素食', '不要肉', '無肉', '寶寶']
- 過濾含肉食譜關鍵詞：['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根', '肉絲', '肉丸']

### 4. 相似度計算
- 使用餘弦相似度
- 範圍：0-1（1表示完全相似）
- 自動排序並返回前三名

## 系統優勢

1. **準確性高**：基於TF-IDF和餘弦相似度的成熟算法
2. **中文友好**：專門針對中文文本進行優化
3. **智能過濾**：自動識別特殊需求（如素食）
4. **易於擴展**：可輕易添加更多食譜或調整參數
5. **用戶友好**：提供清晰的推薦結果和相似度分數

## 未來改進方向

1. 加入更多食材類別
2. 支援營養成分分析
3. 加入用戶評分系統
4. 支援圖片相似度比較
5. 加入季節性推薦功能
