import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
import re
from typing import List, Tuple, Dict
import time

class RecipeRecommendationComparison:
    def __init__(self, csv_file_path: str):
        """
        初始化比較系統
        """
        self.df = pd.read_csv(csv_file_path)
        
        # 版本1：不分詞直接向量化
        self.vectorizer_v1 = TfidfVectorizer(
            max_features=5000,
            stop_words=None,
            ngram_range=(1, 3),  # 增加3-gram來捕捉更多語義
            min_df=1,
            max_df=0.95,
            analyzer='char'  # 字符級分析
        )
        
        # 版本2：分詞後向量化
        self.vectorizer_v2 = TfidfVectorizer(
            max_features=5000,
            stop_words=None,
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95,
            analyzer='word'  # 詞級分析
        )
        
        self.recipe_vectors_v1 = None
        self.recipe_vectors_v2 = None
        self.setup_systems()
    
    def preprocess_text_v1(self, text: str) -> str:
        """
        版本1：不分詞，只做基本清理
        """
        if pd.isna(text):
            return ""
        
        # 移除特殊字符，保留中文、英文、數字
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', '', str(text))
        # 移除多餘空白
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def preprocess_text_v2(self, text: str) -> str:
        """
        版本2：使用jieba分詞
        """
        if pd.isna(text):
            return ""
        
        # 移除特殊字符，保留中文、英文、數字
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', str(text))
        
        # 使用jieba進行中文分詞
        words = jieba.cut(text)
        
        # 過濾空白和單字符
        words = [word.strip() for word in words if len(word.strip()) > 1]
        
        return ' '.join(words)
    
    def setup_systems(self):
        """
        設置兩個版本的推薦系統
        """
        print("正在初始化比較系統...")
        
        # 版本1：不分詞處理
        print("版本1：不分詞直接向量化...")
        start_time = time.time()
        self.df['processed_text_v1'] = self.df['combined_text'].apply(self.preprocess_text_v1)
        recipe_texts_v1 = self.df['processed_text_v1'].tolist()
        self.recipe_vectors_v1 = self.vectorizer_v1.fit_transform(recipe_texts_v1)
        v1_time = time.time() - start_time
        
        # 版本2：分詞處理
        print("版本2：分詞後向量化...")
        start_time = time.time()
        self.df['processed_text_v2'] = self.df['combined_text'].apply(self.preprocess_text_v2)
        recipe_texts_v2 = self.df['processed_text_v2'].tolist()
        self.recipe_vectors_v2 = self.vectorizer_v2.fit_transform(recipe_texts_v2)
        v2_time = time.time() - start_time
        
        print(f"版本1處理時間：{v1_time:.2f}秒，向量維度：{self.recipe_vectors_v1.shape[1]}")
        print(f"版本2處理時間：{v2_time:.2f}秒，向量維度：{self.recipe_vectors_v2.shape[1]}")
        print(f"系統初始化完成！共載入 {len(self.df)} 個食譜")
    
    def get_recommendations_v1(self, user_query: str, top_k: int = 3) -> List[Dict]:
        """
        版本1推薦（不分詞）
        """
        processed_query = self.preprocess_text_v1(user_query)
        query_vector = self.vectorizer_v1.transform([processed_query])
        similarities = cosine_similarity(query_vector, self.recipe_vectors_v1).flatten()
        
        # 檢查素食過濾
        vegetarian_keywords = ['沒有肉', '素食', '不要肉', '無肉', '寶寶']
        is_vegetarian_query = any(keyword in user_query for keyword in vegetarian_keywords)
        
        candidate_count = top_k * 3 if is_vegetarian_query else top_k
        top_indices = similarities.argsort()[-candidate_count:][::-1]
        
        recommendations = []
        meat_keywords = ['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根', '肉絲', '肉丸']
        
        for idx in top_indices:
            recipe = self.df.iloc[idx]
            similarity_score = similarities[idx]
            
            if is_vegetarian_query:
                ingredients_text = recipe['preview_ingredients'] + ' ' + recipe['combined_text']
                if any(meat in ingredients_text for meat in meat_keywords):
                    continue
            
            recommendation = {
                'rank': len(recommendations) + 1,
                'id': recipe['id'],
                'name': recipe['name'],
                'url': recipe['url'],
                'ingredients': recipe['preview_ingredients'],
                'similarity_score': round(similarity_score, 4),
                'steps_preview': recipe['steps'][:80] + '...' if len(recipe['steps']) > 80 else recipe['steps']
            }
            recommendations.append(recommendation)
            
            if len(recommendations) >= top_k:
                break
        
        return recommendations
    
    def get_recommendations_v2(self, user_query: str, top_k: int = 3) -> List[Dict]:
        """
        版本2推薦（分詞）
        """
        processed_query = self.preprocess_text_v2(user_query)
        query_vector = self.vectorizer_v2.transform([processed_query])
        similarities = cosine_similarity(query_vector, self.recipe_vectors_v2).flatten()
        
        # 檢查素食過濾
        vegetarian_keywords = ['沒有肉', '素食', '不要肉', '無肉', '寶寶']
        is_vegetarian_query = any(keyword in user_query for keyword in vegetarian_keywords)
        
        candidate_count = top_k * 3 if is_vegetarian_query else top_k
        top_indices = similarities.argsort()[-candidate_count:][::-1]
        
        recommendations = []
        meat_keywords = ['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根', '肉絲', '肉丸']
        
        for idx in top_indices:
            recipe = self.df.iloc[idx]
            similarity_score = similarities[idx]
            
            if is_vegetarian_query:
                ingredients_text = recipe['preview_ingredients'] + ' ' + recipe['combined_text']
                if any(meat in ingredients_text for meat in meat_keywords):
                    continue
            
            recommendation = {
                'rank': len(recommendations) + 1,
                'id': recipe['id'],
                'name': recipe['name'],
                'url': recipe['url'],
                'ingredients': recipe['preview_ingredients'],
                'similarity_score': round(similarity_score, 4),
                'steps_preview': recipe['steps'][:80] + '...' if len(recipe['steps']) > 80 else recipe['steps']
            }
            recommendations.append(recommendation)
            
            if len(recommendations) >= top_k:
                break
        
        return recommendations
    
    def compare_recommendations(self, user_query: str, top_k: int = 3):
        """
        比較兩個版本的推薦結果
        """
        print(f"\n{'='*80}")
        print(f"🔍 查詢：{user_query}")
        print(f"{'='*80}")
        
        # 獲取兩個版本的推薦
        recs_v1 = self.get_recommendations_v1(user_query, top_k)
        recs_v2 = self.get_recommendations_v2(user_query, top_k)
        
        # 並排顯示結果
        print(f"\n{'版本1 (不分詞)':^40} | {'版本2 (分詞)':^40}")
        print("-" * 40 + " | " + "-" * 40)
        
        for i in range(max(len(recs_v1), len(recs_v2))):
            # 版本1結果
            if i < len(recs_v1):
                v1_text = f"第{recs_v1[i]['rank']}名: {recs_v1[i]['name'][:15]}..."
                v1_score = f"相似度: {recs_v1[i]['similarity_score']:.4f}"
                v1_line = f"{v1_text:<25} {v1_score}"
            else:
                v1_line = " " * 40
            
            # 版本2結果
            if i < len(recs_v2):
                v2_text = f"第{recs_v2[i]['rank']}名: {recs_v2[i]['name'][:15]}..."
                v2_score = f"相似度: {recs_v2[i]['similarity_score']:.4f}"
                v2_line = f"{v2_text:<25} {v2_score}"
            else:
                v2_line = " " * 40
            
            print(f"{v1_line} | {v2_line}")
        
        # 分析差異
        self.analyze_differences(user_query, recs_v1, recs_v2)
    
    def analyze_differences(self, query: str, recs_v1: List[Dict], recs_v2: List[Dict]):
        """
        分析兩個版本的差異
        """
        print(f"\n📊 分析結果：")
        
        # 相似度比較
        if recs_v1 and recs_v2:
            avg_sim_v1 = sum(r['similarity_score'] for r in recs_v1) / len(recs_v1)
            avg_sim_v2 = sum(r['similarity_score'] for r in recs_v2) / len(recs_v2)
            
            print(f"平均相似度 - 版本1: {avg_sim_v1:.4f}, 版本2: {avg_sim_v2:.4f}")
            
            if avg_sim_v1 > avg_sim_v2:
                print("🏆 版本1 (不分詞) 在相似度上表現更好")
            elif avg_sim_v2 > avg_sim_v1:
                print("🏆 版本2 (分詞) 在相似度上表現更好")
            else:
                print("🤝 兩個版本相似度相當")
        
        # 推薦重疊度
        v1_names = set(r['name'] for r in recs_v1)
        v2_names = set(r['name'] for r in recs_v2)
        overlap = len(v1_names & v2_names)
        
        print(f"推薦重疊度: {overlap}/{min(len(recs_v1), len(recs_v2))} ({overlap/min(len(recs_v1), len(recs_v2))*100:.1f}%)")
        
        if overlap == 0:
            print("⚠️  兩個版本推薦完全不同")
        elif overlap == min(len(recs_v1), len(recs_v2)):
            print("✅ 兩個版本推薦完全相同")
        else:
            print("🔄 兩個版本推薦部分重疊")

def main():
    """
    主測試函數
    """
    system = RecipeRecommendationComparison('大白菜_清理後食譜.csv')
    
    # 測試查詢列表
    test_queries = [
        "請推薦大白菜的食譜",
        "我有雞蛋、大白菜和花生米可以做甚麼",
        "清炒大白菜的作法",
        "適合寶寶吃的白菜粥",
        "簡單快速的大白菜料理",
        "沒有肉的大白菜料理",
        # 模糊搜尋測試
        "白菜湯",
        "炒白菜",
        "白菜配肉",
        "酸菜",
        "泡菜"
    ]
    
    print("🔬 食譜推薦系統比較測試")
    print("比較版本1 (不分詞) vs 版本2 (分詞)")
    
    for query in test_queries:
        system.compare_recommendations(query, top_k=3)
        print("\n" + "="*100 + "\n")

if __name__ == "__main__":
    main()
