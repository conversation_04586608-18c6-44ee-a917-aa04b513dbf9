from recipe_recommendation_system import RecipeRecommendationSystem

def demo_single_query():
    """
    演示單一查詢功能
    """
    # 初始化推薦系統
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    print("=== 食譜推薦系統演示 ===\n")
    
    while True:
        print("請輸入您的問題（輸入 'quit' 結束）：")
        user_input = input("> ")
        
        if user_input.lower() == 'quit':
            print("感謝使用！")
            break
        
        if user_input.strip():
            recommendations = system.get_recommendations(user_input, top_k=3)
            system.print_recommendations(user_input, recommendations)
            print("\n" + "="*80 + "\n")

def demo_all_queries():
    """
    演示所有預設查詢
    """
    # 初始化推薦系統
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    # 測試查詢列表
    test_queries = [
        "請推薦大白菜的食譜",
        "我有雞蛋、大白菜和花生米可以做甚麼",
        "清炒大白菜的作法",
        "適合寶寶吃的白菜粥",
        "簡單快速的大白菜料理",
        "沒有肉的大白菜料理"
    ]
    
    print("=== 所有測試查詢結果 ===\n")
    
    # 對每個查詢進行推薦
    for query in test_queries:
        recommendations = system.get_recommendations(query, top_k=3)
        system.print_recommendations(query, recommendations)
        print("\n" + "="*80 + "\n")

def show_system_stats():
    """
    顯示系統統計資訊
    """
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    print("=== 系統統計資訊 ===")
    print(f"總食譜數量：{len(system.df)}")
    print(f"向量維度：{system.recipe_vectors.shape[1]}")
    print(f"平均食譜長度：{system.df['combined_text'].str.len().mean():.1f} 字符")
    
    # 顯示一些食譜名稱樣本
    print("\n食譜名稱樣本：")
    for i, name in enumerate(system.df['name'].head(10)):
        print(f"{i+1}. {name}")

if __name__ == "__main__":
    print("選擇功能：")
    print("1. 演示所有預設查詢")
    print("2. 互動式單一查詢")
    print("3. 顯示系統統計")
    
    choice = input("請輸入選項 (1/2/3): ")
    
    if choice == "1":
        demo_all_queries()
    elif choice == "2":
        demo_single_query()
    elif choice == "3":
        show_system_stats()
    else:
        print("無效選項，執行預設演示...")
        demo_all_queries()
