from recipe_recommendation_system import RecipeRecommendationSystem

def test_specific_queries():
    """
    測試特定的使用者查詢
    """
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    # 指定的測試查詢
    queries = [
        "請推薦大白菜的食譜",
        "我有雞蛋、大白菜和花生米可以做甚麼", 
        "清炒大白菜的作法",
        "適合寶寶吃的白菜粥",
        "簡單快速的大白菜料理",
        "沒有肉的大白菜料理"
    ]
    
    print("🥬 大白菜食譜推薦系統 - 最終測試結果")
    print("=" * 60)
    
    for i, query in enumerate(queries, 1):
        print(f"\n📝 測試查詢 {i}: {query}")
        print("-" * 50)
        
        recommendations = system.get_recommendations(query, top_k=3)
        
        if recommendations:
            for rec in recommendations:
                print(f"🏆 第{rec['rank']}名: {rec['name']}")
                print(f"   相似度: {rec['similarity_score']:.4f}")
                print(f"   食材: {rec['ingredients'][:50]}...")
                print()
        else:
            print("❌ 沒有找到相關推薦")
        
        print("=" * 60)

def analyze_vegetarian_filtering():
    """
    分析素食過濾功能
    """
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    print("\n🌱 素食過濾功能分析")
    print("=" * 40)
    
    # 測試素食查詢
    vegetarian_query = "沒有肉的大白菜料理"
    recommendations = system.get_recommendations(vegetarian_query, top_k=3)
    
    print(f"查詢: {vegetarian_query}")
    print("推薦結果:")
    
    for rec in recommendations:
        ingredients = rec['ingredients']
        has_meat = any(meat in ingredients for meat in ['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根'])
        status = "❌ 含肉" if has_meat else "✅ 素食"
        print(f"  {rec['name']} - {status}")
        print(f"    食材: {ingredients}")
        print()

def performance_summary():
    """
    性能總結
    """
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    print("\n📊 系統性能總結")
    print("=" * 30)
    print(f"📚 食譜總數: {len(system.df)}")
    print(f"🔢 向量維度: {system.recipe_vectors.shape[1]}")
    print(f"📝 平均食譜長度: {system.df['combined_text'].str.len().mean():.1f} 字符")
    
    # 計算相似度分佈
    import numpy as np
    test_query = "大白菜料理"
    processed_query = system.preprocess_text(test_query)
    query_vector = system.vectorizer.transform([processed_query])
    similarities = system.vectorizer.transform([processed_query]).dot(system.recipe_vectors.T).toarray().flatten()
    
    print(f"🎯 相似度統計:")
    print(f"   最高相似度: {similarities.max():.4f}")
    print(f"   平均相似度: {similarities.mean():.4f}")
    print(f"   最低相似度: {similarities.min():.4f}")

if __name__ == "__main__":
    # 執行所有測試
    test_specific_queries()
    analyze_vegetarian_filtering()
    performance_summary()
    
    print("\n🎉 測試完成！系統運行正常。")
    print("💡 提示：您可以使用 demo_recipe_system.py 進行互動式測試。")
