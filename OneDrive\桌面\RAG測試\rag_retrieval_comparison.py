import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
import re
from typing import List, Dict
import time

class RAGWithTagsRetrieval:
    def __init__(self, csv_file_path: str):
        """
        RAG系統 - 使用標籤向量化進行檢索
        """
        self.df = pd.read_csv(csv_file_path)
        
        # 標籤向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=2000,
            stop_words=None,
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95,
            analyzer='word'
        )
        
        self.recipe_vectors = None
        self.setup_system()
    
    def extract_comprehensive_tags(self, recipe: pd.Series) -> str:
        """
        提取綜合標籤（食材 + 烹飪方法 + 特殊屬性）
        """
        all_tags = []
        
        # 1. 食材標籤
        ingredients = self.extract_ingredients_tags(recipe['preview_ingredients'])
        all_tags.extend(ingredients)
        
        # 2. 烹飪方法標籤
        cooking_methods = self.extract_cooking_methods(recipe['combined_text'])
        all_tags.extend(cooking_methods)
        
        # 3. 特殊屬性標籤
        attributes = self.extract_special_attributes(recipe['combined_text'])
        all_tags.extend(attributes)
        
        return ' '.join(all_tags)
    
    def extract_ingredients_tags(self, ingredients_text: str) -> List[str]:
        """
        提取食材標籤
        """
        if pd.isna(ingredients_text):
            return []
        
        text = str(ingredients_text)
        
        # 移除數量和單位
        quantity_patterns = [
            r'\d+\.?\d*\s*[克公斤毫升大小匙茶匙杯碗盒包片根顆個條支把]',
            r'適量', r'少許', r'一些', r'半個', r'半顆', r'半盒',
            r'\d+\.?\d*', r'[0-9]+',
        ]
        
        for pattern in quantity_patterns:
            text = re.sub(pattern, ' ', text)
        
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', text)
        words = jieba.cut(text)
        
        stop_words = {'的', '和', '或', '及', '與', '用', '加', '放', '切', '片', '絲', '塊', '段', '可省'}
        ingredients = []
        
        for word in words:
            word = word.strip()
            if len(word) > 1 and word not in stop_words:
                ingredients.append(word)
        
        return ingredients
    
    def extract_cooking_methods(self, text: str) -> List[str]:
        """
        提取烹飪方法標籤
        """
        methods = []
        text_str = str(text)
        
        method_keywords = {
            '炒': ['炒', '清炒', '爆炒'],
            '煮': ['煮', '水煮', '燉煮'],
            '蒸': ['蒸', '清蒸'],
            '燉': ['燉', '燉煮'],
            '燒': ['燒', '紅燒'],
            '煎': ['煎', '煎炸'],
            '拌': ['拌', '涼拌'],
            '滷': ['滷', '滷製'],
            '粥': ['粥', '煮粥'],
            '湯': ['湯', '煮湯']
        }
        
        for method, keywords in method_keywords.items():
            if any(keyword in text_str for keyword in keywords):
                methods.append(method)
        
        return methods
    
    def extract_special_attributes(self, text: str) -> List[str]:
        """
        提取特殊屬性標籤
        """
        attributes = []
        text_str = str(text)
        
        # 飲食偏好
        if any(word in text_str for word in ['素食', '素', '五辛素']):
            attributes.append('素食')
        
        # 難度
        if any(word in text_str for word in ['簡單', '容易', '快速', '十分鐘']):
            attributes.append('簡單')
        
        # 特殊對象
        if any(word in text_str for word in ['寶寶', '嬰兒', '幼兒']):
            attributes.append('寶寶適合')
        
        # 口味
        if '辣' in text_str:
            attributes.append('辣味')
        if '酸' in text_str:
            attributes.append('酸味')
        
        return attributes
    
    def setup_system(self):
        """
        設置RAG標籤檢索系統
        """
        print("正在初始化RAG標籤檢索系統...")
        start_time = time.time()
        
        # 為每個食譜創建綜合標籤
        self.df['comprehensive_tags'] = self.df.apply(self.extract_comprehensive_tags, axis=1)
        
        # 向量化標籤
        tags_texts = self.df['comprehensive_tags'].tolist()
        self.recipe_vectors = self.vectorizer.fit_transform(tags_texts)
        
        setup_time = time.time() - start_time
        print(f"RAG標籤檢索系統初始化完成！")
        print(f"處理時間: {setup_time:.2f}秒")
        print(f"向量維度: {self.recipe_vectors.shape[1]}")
        
        # 顯示標籤示例
        print("\n標籤示例：")
        for i in range(3):
            print(f"食譜: {self.df.iloc[i]['name']}")
            print(f"標籤: {self.df.iloc[i]['comprehensive_tags']}")
            print("-" * 40)
    
    def retrieve_with_tags(self, user_query: str, top_k: int = 5) -> List[Dict]:
        """
        使用標籤進行檢索
        """
        # 預處理查詢
        processed_query = ' '.join(jieba.cut(user_query))
        
        # 計算相似度
        query_vector = self.vectorizer.transform([processed_query])
        similarities = cosine_similarity(query_vector, self.recipe_vectors).flatten()
        
        # 素食過濾
        vegetarian_keywords = ['沒有肉', '素食', '不要肉', '無肉', '寶寶']
        is_vegetarian_query = any(keyword in user_query for keyword in vegetarian_keywords)
        
        candidate_count = top_k * 2 if is_vegetarian_query else top_k
        top_indices = similarities.argsort()[-candidate_count:][::-1]
        
        retrieved_recipes = []
        meat_keywords = ['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根', '肉絲', '肉丸']
        
        for idx in top_indices:
            recipe = self.df.iloc[idx]
            similarity_score = similarities[idx]
            
            if is_vegetarian_query:
                ingredients_text = recipe['preview_ingredients'] + ' ' + recipe['combined_text']
                if any(meat in ingredients_text for meat in meat_keywords):
                    continue
            
            retrieved_recipe = {
                'id': recipe['id'],
                'name': recipe['name'],
                'url': recipe['url'],
                'ingredients': recipe['preview_ingredients'],
                'steps': recipe['steps'],
                'similarity_score': round(similarity_score, 4),
                'matched_tags': recipe['comprehensive_tags'],
                'full_content': recipe['combined_text']
            }
            retrieved_recipes.append(retrieved_recipe)
            
            if len(retrieved_recipes) >= top_k:
                break
        
        return retrieved_recipes

class RAGWithFullTextRetrieval:
    def __init__(self, csv_file_path: str):
        """
        RAG系統 - 使用整篇食譜向量化進行檢索
        """
        self.df = pd.read_csv(csv_file_path)
        
        # 整篇文本向量化器
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words=None,
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95,
            analyzer='word',
            sublinear_tf=True,
            smooth_idf=True
        )
        
        self.recipe_vectors = None
        self.setup_system()
    
    def preprocess_text(self, text: str) -> str:
        """
        文本預處理
        """
        if pd.isna(text):
            return ""
        
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', str(text))
        words = jieba.cut(text)
        words = [word.strip() for word in words if len(word.strip()) > 1]
        return ' '.join(words)
    
    def setup_system(self):
        """
        設置RAG整篇文本檢索系統
        """
        print("正在初始化RAG整篇文本檢索系統...")
        start_time = time.time()
        
        # 預處理所有食譜文本
        self.df['processed_text'] = self.df['combined_text'].apply(self.preprocess_text)
        recipe_texts = self.df['processed_text'].tolist()
        self.recipe_vectors = self.vectorizer.fit_transform(recipe_texts)
        
        setup_time = time.time() - start_time
        print(f"RAG整篇文本檢索系統初始化完成！")
        print(f"處理時間: {setup_time:.2f}秒")
        print(f"向量維度: {self.recipe_vectors.shape[1]}")
    
    def retrieve_with_full_text(self, user_query: str, top_k: int = 5) -> List[Dict]:
        """
        使用整篇文本進行檢索
        """
        # 預處理查詢
        processed_query = self.preprocess_text(user_query)
        
        # 計算相似度
        query_vector = self.vectorizer.transform([processed_query])
        similarities = cosine_similarity(query_vector, self.recipe_vectors).flatten()
        
        # 素食過濾
        vegetarian_keywords = ['沒有肉', '素食', '不要肉', '無肉', '寶寶']
        is_vegetarian_query = any(keyword in user_query for keyword in vegetarian_keywords)
        
        candidate_count = top_k * 2 if is_vegetarian_query else top_k
        top_indices = similarities.argsort()[-candidate_count:][::-1]
        
        retrieved_recipes = []
        meat_keywords = ['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根', '肉絲', '肉丸']
        
        for idx in top_indices:
            recipe = self.df.iloc[idx]
            similarity_score = similarities[idx]
            
            if is_vegetarian_query:
                ingredients_text = recipe['preview_ingredients'] + ' ' + recipe['combined_text']
                if any(meat in ingredients_text for meat in meat_keywords):
                    continue
            
            retrieved_recipe = {
                'id': recipe['id'],
                'name': recipe['name'],
                'url': recipe['url'],
                'ingredients': recipe['preview_ingredients'],
                'steps': recipe['steps'],
                'similarity_score': round(similarity_score, 4),
                'full_content': recipe['combined_text']
            }
            retrieved_recipes.append(retrieved_recipe)
            
            if len(retrieved_recipes) >= top_k:
                break
        
        return retrieved_recipes

def simulate_llm_generation(user_query: str, retrieved_recipes: List[Dict], retrieval_method: str) -> Dict:
    """
    模擬LLM生成（兩種檢索方法共用）
    """
    if not retrieved_recipes:
        return {
            'type': 'no_results',
            'message': '抱歉，沒有找到符合您需求的食譜。',
            'retrieval_method': retrieval_method,
            'quality_score': 0.0
        }
    
    # 分析查詢意圖
    query_lower = user_query.lower()
    
    if any(word in query_lower for word in ['我有', '手邊有']):
        intent = 'ingredient_based'
    elif any(word in query_lower for word in ['作法', '怎麼做', '如何']):
        intent = 'cooking_method'
    elif any(word in query_lower for word in ['寶寶', '素食', '沒有肉']):
        intent = 'dietary_preference'
    else:
        intent = 'general'
    
    # 基於檢索結果生成回應
    best_recipe = retrieved_recipes[0]
    
    response_text = f"根據您的查詢「{user_query}」，我為您推薦：\n\n"
    response_text += f"🍽️ **{best_recipe['name']}**\n"
    response_text += f"📊 檢索相關度：{best_recipe['similarity_score']:.2%}\n"
    response_text += f"🥬 主要食材：{best_recipe['ingredients']}\n"
    response_text += f"👨‍🍳 製作步驟：{best_recipe['steps'][:100]}...\n"
    response_text += f"🔗 完整食譜：{best_recipe['url']}\n\n"
    
    # 添加其他推薦
    if len(retrieved_recipes) > 1:
        response_text += "其他推薦：\n"
        for i, recipe in enumerate(retrieved_recipes[1:3], 2):
            response_text += f"{i}. {recipe['name']} (相關度: {recipe['similarity_score']:.2%})\n"
    
    # 計算生成質量分數
    quality_score = calculate_generation_quality(user_query, retrieved_recipes, intent)
    
    return {
        'type': intent,
        'message': response_text,
        'primary_recommendation': best_recipe,
        'alternative_recommendations': retrieved_recipes[1:3],
        'retrieval_method': retrieval_method,
        'quality_score': quality_score
    }

def calculate_generation_quality(user_query: str, retrieved_recipes: List[Dict], intent: str) -> float:
    """
    計算生成質量分數
    """
    if not retrieved_recipes:
        return 0.0
    
    # 基礎分數
    base_score = 0.6
    
    # 檢索質量加分
    avg_similarity = sum(r['similarity_score'] for r in retrieved_recipes) / len(retrieved_recipes)
    base_score += avg_similarity * 0.3
    
    # 意圖匹配加分
    if intent == 'ingredient_based' and '我有' in user_query:
        base_score += 0.1
    elif intent == 'cooking_method' and '作法' in user_query:
        base_score += 0.1
    elif intent == 'dietary_preference' and ('寶寶' in user_query or '素食' in user_query):
        base_score += 0.1
    
    return min(base_score, 1.0)

def comprehensive_rag_retrieval_comparison():
    """
    全面比較RAG中的兩種檢索方法
    """
    print("🔬 RAG檢索方法比較：標籤向量化 vs 整篇食譜向量化")
    print("=" * 70)
    
    # 初始化兩個系統
    tags_system = RAGWithTagsRetrieval('大白菜_清理後食譜.csv')
    full_system = RAGWithFullTextRetrieval('大白菜_清理後食譜.csv')
    
    # 測試查詢
    test_queries = [
        "適合寶寶吃的白菜粥",
        "我有雞蛋、大白菜和花生米可以做甚麼",
        "清炒大白菜的作法",
        "簡單快速的大白菜料理",
        "沒有肉的大白菜料理",
        "請推薦大白菜的食譜"
    ]
    
    print(f"\n📊 逐項查詢比較...")
    
    tags_wins = 0
    full_wins = 0
    ties = 0
    
    tags_total_score = 0
    full_total_score = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. 查詢: '{query}'")
        print("-" * 60)
        
        # 標籤檢索 + LLM生成
        start_time = time.time()
        tags_retrieved = tags_system.retrieve_with_tags(query, 5)
        tags_response = simulate_llm_generation(query, tags_retrieved, "標籤檢索")
        tags_time = time.time() - start_time
        
        # 整篇文本檢索 + LLM生成
        start_time = time.time()
        full_retrieved = full_system.retrieve_with_full_text(query, 5)
        full_response = simulate_llm_generation(query, full_retrieved, "整篇文本檢索")
        full_time = time.time() - start_time
        
        # 比較結果
        tags_score = tags_response['quality_score']
        full_score = full_response['quality_score']
        
        tags_total_score += tags_score
        full_total_score += full_score
        
        print(f"  標籤檢索RAG: {tags_score:.4f} ({tags_time*1000:.1f}ms)")
        print(f"    檢索到: {len(tags_retrieved)}個食譜")
        print(f"    第1推薦: {tags_response['primary_recommendation']['name'] if tags_response['type'] != 'no_results' else '無推薦'}")
        
        print(f"  整篇檢索RAG: {full_score:.4f} ({full_time*1000:.1f}ms)")
        print(f"    檢索到: {len(full_retrieved)}個食譜")
        print(f"    第1推薦: {full_response['primary_recommendation']['name'] if full_response['type'] != 'no_results' else '無推薦'}")
        
        # 判斷勝負
        if tags_score > full_score:
            tags_wins += 1
            print("  🏆 標籤檢索RAG勝出")
        elif full_score > tags_score:
            full_wins += 1
            print("  🏆 整篇檢索RAG勝出")
        else:
            ties += 1
            print("  🤝 平手")
    
    # 生成總結報告
    print(f"\n📈 RAG檢索方法比較總結")
    print("=" * 40)
    
    print(f"\n🏆 勝出統計:")
    print(f"標籤檢索RAG: {tags_wins}/{len(test_queries)} ({tags_wins/len(test_queries)*100:.1f}%)")
    print(f"整篇檢索RAG: {full_wins}/{len(test_queries)} ({full_wins/len(test_queries)*100:.1f}%)")
    print(f"平手: {ties}/{len(test_queries)} ({ties/len(test_queries)*100:.1f}%)")
    
    print(f"\n📊 平均質量分數:")
    print(f"標籤檢索RAG: {tags_total_score/len(test_queries):.4f}")
    print(f"整篇檢索RAG: {full_total_score/len(test_queries):.4f}")
    
    # 最終建議
    print(f"\n💡 RAG檢索方法建議:")
    print("=" * 25)
    
    if full_wins > tags_wins:
        print("🏆 推薦: RAG + 整篇食譜檢索")
        print("理由:")
        print(f"  - 勝出次數更多 ({full_wins} vs {tags_wins})")
        print(f"  - 平均質量分數更高")
        print(f"  - 語義理解更全面")
    elif tags_wins > full_wins:
        print("🏆 推薦: RAG + 標籤檢索")
        print("理由:")
        print(f"  - 勝出次數更多 ({tags_wins} vs {full_wins})")
        print(f"  - 檢索更精準")
    else:
        print("🤝 兩種方法效果相當")
        print("建議根據具體需求選擇")

def main():
    """
    主函數
    """
    comprehensive_rag_retrieval_comparison()

if __name__ == "__main__":
    main()
