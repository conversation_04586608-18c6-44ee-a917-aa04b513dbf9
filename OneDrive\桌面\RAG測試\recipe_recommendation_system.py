import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
import re
from typing import List, Tuple, Dict

class RecipeRecommendationSystem:
    def __init__(self, csv_file_path: str):
        """
        初始化食譜推薦系統
        
        Args:
            csv_file_path: CSV檔案路徑
        """
        self.df = pd.read_csv(csv_file_path)
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words=None,
            ngram_range=(1, 2),
            min_df=1,
            max_df=0.95
        )
        self.recipe_vectors = None
        self.setup_system()
    
    def preprocess_text(self, text: str) -> str:
        """
        文本預處理
        
        Args:
            text: 原始文本
            
        Returns:
            處理後的文本
        """
        if pd.isna(text):
            return ""
        
        # 移除特殊字符，保留中文、英文、數字
        text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', str(text))
        
        # 使用jieba進行中文分詞
        words = jieba.cut(text)
        
        # 過濾空白和單字符
        words = [word.strip() for word in words if len(word.strip()) > 1]
        
        return ' '.join(words)
    
    def setup_system(self):
        """
        設置推薦系統，進行向量化
        """
        print("正在初始化推薦系統...")
        
        # 預處理食譜文本
        print("正在預處理食譜文本...")
        self.df['processed_text'] = self.df['combined_text'].apply(self.preprocess_text)
        
        # 建立TF-IDF向量
        print("正在建立TF-IDF向量...")
        recipe_texts = self.df['processed_text'].tolist()
        self.recipe_vectors = self.vectorizer.fit_transform(recipe_texts)
        
        print(f"系統初始化完成！共載入 {len(self.df)} 個食譜")
    
    def get_recommendations(self, user_query: str, top_k: int = 3) -> List[Dict]:
        """
        根據使用者查詢獲取推薦食譜

        Args:
            user_query: 使用者查詢
            top_k: 返回前k個推薦

        Returns:
            推薦食譜列表
        """
        # 預處理使用者查詢
        processed_query = self.preprocess_text(user_query)

        # 將查詢轉換為向量
        query_vector = self.vectorizer.transform([processed_query])

        # 計算餘弦相似度
        similarities = cosine_similarity(query_vector, self.recipe_vectors).flatten()

        # 檢查是否需要素食過濾
        vegetarian_keywords = ['沒有肉', '素食', '不要肉', '無肉', '寶寶']
        is_vegetarian_query = any(keyword in user_query for keyword in vegetarian_keywords)

        # 獲取候選食譜（取更多候選以便過濾）
        candidate_count = top_k * 3 if is_vegetarian_query else top_k
        top_indices = similarities.argsort()[-candidate_count:][::-1]

        # 準備推薦結果
        recommendations = []
        meat_keywords = ['豬肉', '牛肉', '雞肉', '肉片', '絞肉', '火腿', '培根', '肉絲', '肉丸']

        for idx in top_indices:
            recipe = self.df.iloc[idx]
            similarity_score = similarities[idx]

            # 如果是素食查詢，過濾含肉的食譜
            if is_vegetarian_query:
                ingredients_text = recipe['preview_ingredients'] + ' ' + recipe['combined_text']
                if any(meat in ingredients_text for meat in meat_keywords):
                    continue

            recommendation = {
                'rank': len(recommendations) + 1,
                'id': recipe['id'],
                'name': recipe['name'],
                'url': recipe['url'],
                'ingredients': recipe['preview_ingredients'],
                'similarity_score': round(similarity_score, 4),
                'steps_preview': recipe['steps'][:100] + '...' if len(recipe['steps']) > 100 else recipe['steps']
            }
            recommendations.append(recommendation)

            # 達到所需數量就停止
            if len(recommendations) >= top_k:
                break

        return recommendations
    
    def print_recommendations(self, user_query: str, recommendations: List[Dict]):
        """
        格式化輸出推薦結果
        
        Args:
            user_query: 使用者查詢
            recommendations: 推薦結果
        """
        print(f"\n{'='*60}")
        print(f"使用者問題：{user_query}")
        print(f"{'='*60}")
        
        if not recommendations:
            print("抱歉，沒有找到相關的食譜推薦。")
            return
        
        for rec in recommendations:
            print(f"\n【第 {rec['rank']} 名推薦】")
            print(f"食譜名稱：{rec['name']}")
            print(f"相似度分數：{rec['similarity_score']}")
            print(f"主要食材：{rec['ingredients']}")
            print(f"製作步驟預覽：{rec['steps_preview']}")
            print(f"詳細食譜：{rec['url']}")
            print("-" * 50)

def main():
    """
    主函數，測試推薦系統
    """
    # 初始化推薦系統
    system = RecipeRecommendationSystem('大白菜_清理後食譜.csv')
    
    # 測試查詢列表
    test_queries = [
        "請推薦大白菜的食譜",
        "我有雞蛋、大白菜和花生米可以做甚麼",
        "清炒大白菜的作法",
        "適合寶寶吃的白菜粥",
        "簡單快速的大白菜料理",
        "沒有肉的大白菜料理"
    ]
    
    # 對每個查詢進行推薦
    for query in test_queries:
        recommendations = system.get_recommendations(query, top_k=3)
        system.print_recommendations(query, recommendations)
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()
