import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
import re

class RecipeRecommendationSystem:
    def __init__(self, csv_file_path): """初始化推薦系統"""
        self.df = pd.read_csv(csv_file_path)
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,
            ngram_range=(1,
2)
        )
        self.recipe_vectors = None
        self._prepare_data()
        self._vectorize_recipes()
    
    def _prepare_data(self): """準備和清理數據"""
        # 合併食譜的所有文本信息
        self.df['combined_text'
] = (
            self.df['name'
].fillna('') + ' ' +
            self.df['ingredients'
].fillna('') + ' ' +
            self.df['steps'
].fillna('')
        )
        
        # 使用jieba進行中文分詞
        self.df['segmented_text'
] = self.df['combined_text'
].apply(
            lambda x: ' '.join(jieba.cut(str(x)))
        )
    
    def _vectorize_recipes(self): """將食譜向量化"""
        self.recipe_vectors = self.vectorizer.fit_transform(self.df['segmented_text'
])
    
    def recommend_recipes(self, query, top_k=3): """根據查詢推薦食譜"""
        # 對查詢進行分詞
        segmented_query = ' '.join(jieba.cut(query))
        
        # 將查詢向量化
        query_vector = self.vectorizer.transform([segmented_query
])
        
        # 計算餘弦相似度
        similarities = cosine_similarity(query_vector, self.recipe_vectors).flatten()
        
        # 獲取最相似的食譜索引
        top_indices = similarities.argsort()[-top_k:
][
    : : -1
]
        
        # 返回推薦結果
        recommendations = []
        for idx in top_indices:
            recipe = self.df.iloc[idx
]
            recommendations.append({
                'name': recipe['name'
    ],
                'similarity': similarities[idx
    ],
                'ingredients': recipe['ingredients'
    ],
                'steps': recipe['steps'
    ][
        : 200
    ] + '...' if len(str(recipe['steps'
    ])) > 200 else recipe['steps'
    ],
                'url': recipe['url'
    ]
})
        
        return recommendations

def main():
    # 初始化推薦系統
    recommender = RecipeRecommendationSystem('OneDrive/桌面/RAG測試/大白菜_清理後食譜.csv')
    
    # 測試查詢
    queries = [
    "請推薦大白菜的食譜",
    "我有雞蛋、大白菜和花生米可以做甚麼",
    "清炒大白菜的作法",
    "適合寶寶吃的白菜粥",
    "簡單快速的大白菜料理",
    "沒有肉的大白菜料理"
]
    
    for i, query in enumerate(queries,
1):
        print(f"\n{'='*50}")
        print(f"查詢 {i}: {query}")
        print('='*50)
        
        recommendations = recommender.recommend_recipes(query, top_k=3)
        
        for j, rec in enumerate(recommendations,
1):
            print(f"\n推薦 {j}: {rec['name']}")
            print(f"相似度: {rec['similarity']:.4f}")
            print(f"食材: {rec['ingredients']}")
            print(f"做法: {rec['steps']}")
            print(f"網址: {rec['url']}")
            print("-" * 30)

if __name__ == "__main__":
    main()

