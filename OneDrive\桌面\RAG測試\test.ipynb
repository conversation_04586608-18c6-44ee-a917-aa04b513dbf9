from sentence_transformers import SentenceTransformer
import faiss
import numpy as np

# 模擬一些中文食譜資料（title + ingredients + steps）
recipes = [
    "青江菜炒蛋：食材有青江菜、雞蛋、鹽巴。將青江菜切段，雞蛋打散後拌炒。",
    "胡蘿蔔炒蛋：食材有胡蘿蔔、雞蛋。將胡蘿蔔刨絲後與蛋一起炒。",
    "番茄炒蛋：食材有番茄、雞蛋。將番茄切塊、炒蛋後加入番茄。",
    "青江菜豆腐湯：食材有青江菜、豆腐、薑片。青江菜川燙，與豆腐煮湯。",
    "三色蔬菜炒：青江菜、紅蘿蔔、玉米筍，快炒即成。",
]

# 初始化中文語意模型（支持中文的 multilingual 模型）
model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')

# 向量化每篇食譜
embeddings = model.encode(recipes, convert_to_numpy=True)

# 建立 FAISS 索引
embedding_dim = embeddings.shape[1]
index = faiss.IndexFlatL2(embedding_dim)
index.add(embeddings)

# 儲存食譜對應（這是對照用，回傳結果時用）
id2recipe = {i: recipe for i, recipe in enumerate(recipes)}

# === 查詢範例 ===
def search_recipe(query, top_k=3):
    query_vector = model.encode([query], convert_to_numpy=True)
    distances, indices = index.search(query_vector, top_k)
    print(f"\n🔍 查詢：「{query}」\n")
    for i, idx in enumerate(indices[0]):
        print(f"Top {i+1}:")
        print(f"▶ {id2recipe[idx]}")
        print(f"距離: {distances[0][i]:.4f}\n")

# === 使用測試 ===
search_recipe("我有青江菜和雞蛋可以煮什麼")
search_recipe("推薦一個含胡蘿蔔的食譜")
