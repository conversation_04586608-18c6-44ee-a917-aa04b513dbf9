from sentence_transformers import SentenceTransformer
import faiss
import pandas as pd
import numpy as np

# 讀取資料
df = pd.read_csv("大白菜_清理後食譜.csv")

# 取出需要的欄位（使用 combined_text 作為語意基礎）
texts = df["combined_text"].fillna("").tolist()

# 建立語意模型（多語言、支援中文）
model = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")

# 向量化所有食譜文字
embeddings = model.encode(texts, convert_to_numpy=True)

# 建立 FAISS 索引
embedding_dim = embeddings.shape[1]
index = faiss.IndexFlatL2(embedding_dim)
index.add(embeddings)

# 搜尋函式：根據使用者輸入查找最相近的食譜
def search_recipe(query, top_k=3):
    query_vec = model.encode([query], convert_to_numpy=True)
    distances, indices = index.search(query_vec, top_k)

    print(f"\n🔍 查詢關鍵字：「{query}」\n")
    for i, idx in enumerate(indices[0]):
        row = df.iloc[int(idx)]
        print(f"--- Top {i+1} ---")
        print(f"📌 食譜名稱：{row['name']}")
        print(f"🥬 食材：{row['ingredients']}")
        print(f"📝 步驟：{row['steps']}")
        print(f"🔗 網址：{row['url']}")
        print(f"📷 圖片路徑：{row['image_path']}")
        print(f"📐 距離（越小越像）：{distances[0][i]:.4f}\n")

# ✅ 使用範例
search_recipe("大白菜鹹粥")


import pandas as pd
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np

# 讀取 CSV
df = pd.read_csv('大白菜_清理後食譜.csv')

# 食材欄位轉成 list（以 、 或 | 分隔都可）
df['ingredient_list'] = df['ingredients'].str.replace(' ', '').str.split('[、|]')



model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')

# 將每筆食譜的食材列表轉成一個字串，例如「雞蛋 胡蘿蔔 青江菜」
df['ingredient_text'] = df['ingredient_list'].apply(lambda x: ' '.join(x))

# 向量化
ingredient_embeddings = model.encode(df['ingredient_text'].tolist(), convert_to_numpy=True)

# FAISS index 建立
index_1 = faiss.IndexFlatL2(ingredient_embeddings.shape[1])
index_1.add(ingredient_embeddings)

query = "大白菜 雞蛋"
query_vec = model.encode([query])
D, I = index_1.search(query_vec, k=5)

for idx in I[0]:
    print(df.iloc[idx]['name'], "➡", df.iloc[idx]['ingredient_text'])



# 向量化整篇食譜描述
full_embeddings = model.encode(df['combined_text'].tolist(), convert_to_numpy=True)

# FAISS index
index_2 = faiss.IndexFlatL2(full_embeddings.shape[1])
index_2.add(full_embeddings)

query = "大白菜 雞蛋"
query_vec = model.encode([query])
D, I = index_2.search(query_vec, k=5)

for idx in I[0]:
    print(df.iloc[idx]['name'], "➡", df.iloc[idx]['combined_text'][:60], "...")


from transformers import pipeline

# 模擬 RAG：取出 top-3 內容組合成 context
context = "\n\n".join(df.iloc[I[0][:3]]['combined_text'].tolist())

# 問法
question = "我有雞蛋 青江菜 胡蘿蔔，可以煮什麼？"

rag_prompt = f"根據以下食譜內容，回答使用者的問題：\n\n{context}\n\n問題：{question}\n\n回答："

qa = pipeline("text-generation", model="tiiuae/falcon-7b-instruct", tokenizer="tiiuae/falcon-7b-instruct")
result = qa(rag_prompt, max_new_tokens=100)
print(result[0]['generated_text'])
